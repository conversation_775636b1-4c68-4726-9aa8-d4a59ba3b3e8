<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改识别码')" />
</head>
<body>
    <div class="main-content">
        <form class="form-horizontal" id="form-categoryCode-edit" th:object="${categoryCode}">
            <input name="categoryId" th:field="*{categoryId}" type="hidden">
            <input name="conference" th:field="*{conference}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label">识别码：</label>
                <div class="col-sm-8">
                    <input class="form-control" type="text" th:field="*{categoryId}" readonly>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">会议：</label>
                <div class="col-sm-8">
                    <select class="form-control" disabled>
                        <option th:each="conference : ${conferences}" th:value="${conference.id}" th:text="${conference.conferenceTitle}" th:selected="${conference.id == categoryCode.conference}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">房间：</label>
                <div class="col-sm-8">
                    <select name="roomId" class="form-control">
                        <option value="">请选择房间</option>
                        <option th:each="room : ${rooms}" th:value="${room.id}" th:text="${room.roomTitle}" th:selected="${room.id == categoryCode.roomId}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">房间数量：</label>
                <div class="col-sm-8">
                    <input name="roomCount" th:field="*{roomCount}" class="form-control" type="number" min="1">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "hotel/categoryCode";
        $("#form-categoryCode-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.ajax({
                    url: prefix + "/edit",
                    type: "post",
                    data: $('#form-categoryCode-edit').serialize(),
                    beforeSend: function (xhr, settings) {
                        var csrftoken = $('meta[name=csrf-token]').attr('content');
                        xhr.setRequestHeader("X-CSRF-Token", csrftoken);
                        $.modal.loading("正在处理中，请稍候...");
                    },
                    success: function(result) {
                        // 使用RuoYi框架的标准成功回调，会自动处理消息显示、表格刷新和窗口关闭
                        $.operate.successCallback(result);
                    }
                });
            }
        }
    </script>
</body>
</html>
