<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增识别码')" />
</head>
<body>
    <div class="main-content">
        <form class="form-horizontal" id="form-categoryCode-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">识别码：</label>
                <div class="col-sm-8">
                    <input name="categoryId" class="form-control" type="text" required maxlength="8" placeholder="请输入6-8位识别码">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">会议：</label>
                <div class="col-sm-8">
                    <select name="conference" class="form-control" required onchange="loadRooms(this.value)">
                        <option value="">请选择会议</option>
                        <option th:each="conference : ${conferences}" th:value="${conference.id}" th:text="${conference.conferenceTitle}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">房间：</label>
                <div class="col-sm-8">
                    <select name="roomId" class="form-control" id="roomSelect">
                        <option value="">请先选择会议</option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">房间数量：</label>
                <div class="col-sm-8">
                    <input name="roomCount" class="form-control" type="number" min="1" placeholder="请输入房间数量">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "hotel/categoryCode";
        var roomPrefix = ctx + "hotel/room";
        
        $("#form-categoryCode-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.ajax({
                    url: prefix + "/add",
                    type: "post",
                    data: $('#form-categoryCode-add').serialize(),
                    beforeSend: function (xhr, settings) {
                        var csrftoken = $('meta[name=csrf-token]').attr('content');
                        xhr.setRequestHeader("X-CSRF-Token", csrftoken);
                        $.modal.loading("正在处理中，请稍候...");
                    },
                    success: function(result) {
                        // 使用RuoYi框架的标准成功回调，会自动处理消息显示、表格刷新和窗口关闭
                        $.operate.successCallback(result);
                    }
                });
            }
        }

        function loadRooms(conferenceId) {
            var roomSelect = $("#roomSelect");
            roomSelect.empty();
            
            if (conferenceId) {
                $.ajax({
                    url: roomPrefix + "/listByConference",
                    type: "post",
                    data: { conferenceId: conferenceId },
                    success: function(result) {
                        if (result.code == 0) {
                            roomSelect.append('<option value="">请选择房间</option>');
                            $.each(result.data, function(index, room) {
                                roomSelect.append('<option value="' + room.id + '">' + room.roomTitle + '</option>');
                            });
                        } else {
                            roomSelect.append('<option value="">加载房间失败</option>');
                        }
                    },
                    error: function() {
                        roomSelect.append('<option value="">加载房间失败</option>');
                    }
                });
            } else {
                roomSelect.append('<option value="">请先选择会议</option>');
            }
        }
    </script>
</body>
</html>
